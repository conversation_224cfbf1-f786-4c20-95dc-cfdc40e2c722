#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTP结构体解析工具 - Python版本
从C++头文件解析结构体定义并生成带padding的Python ctypes定义
"""

import re
import os
from dataclasses import dataclass
from typing import List, Dict, Optional

@dataclass
class TypeInfo:
    """类型定义信息"""
    type_name: str
    base_type: str
    array_size: int = 0
    is_array: bool = False
    total_size: int = 1
    alignment: int = 1  # 对齐要求

@dataclass
class FieldInfo:
    """字段信息"""
    field_name: str
    type_name: str
    base_type: str
    comment: str
    cn_name: str
    size: int
    offset: int
    alignment: int
    is_array: bool = False
    array_size: int = 0
    is_padding: bool = False  # 是否是padding字段

@dataclass
class StructInfo:
    """结构体信息"""
    cpp_name: str
    python_name: str
    description: str
    fields: List[FieldInfo]
    total_size: int = 0
    aligned_size: int = 0  # 对齐后的大小

class CTPStructParser:
    """CTP结构体解析器"""
    
    def __init__(self):
        self.type_definitions: Dict[str, TypeInfo] = {}
        self.target_structs = [
            ("CThostFtdcTradingAccountField", "TradingAccountField", "资金账户数据结构"),
            ("CThostFtdcInstrumentField", "InstrumentField", "合约数据结构"),
            ("CThostFtdcInvestorPositionField", "InvestorPositionField", "持仓数据结构"),
            ("CThostFtdcTradeField", "TradeField", "成交数据结构"),
            ("CThostFtdcOrderField", "OrderField", "报单数据结构")
        ]
    
    def get_type_alignment(self, base_type: str, is_array: bool = False) -> int:
        """获取类型的对齐要求"""
        if is_array:
            return 1  # 数组按1字节对齐
        
        alignment_map = {
            'char': 1,
            'short': 2,
            'int': 4,
            'double': 8,
            'float': 4,
            'long': 8,
            'long long': 8
        }
        return alignment_map.get(base_type, 1)
    
    def parse_typedefs(self, header_file: str) -> None:
        """解析typedef定义"""
        if not os.path.exists(header_file):
            print(f"# 错误: 头文件不存在 {header_file}")
            return
        
        with open(header_file, 'r', encoding='gb2312', errors='ignore') as f:
            content = f.read()
        
        # 匹配typedef定义
        typedef_pattern = r'typedef\s+(\w+)\s+(\w+(?:\[\d+\])?)\s*;'
        matches = re.findall(typedef_pattern, content)
        
        for base_type, new_type in matches:
            # 检查是否是数组类型
            array_match = re.match(r'(\w+)\[(\d+)\]', new_type)
            if array_match:
                type_name = array_match.group(1)
                array_size = int(array_match.group(2))
                is_array = True
                total_size = array_size
                alignment = 1  # 数组按1字节对齐
            else:
                type_name = new_type
                array_size = 0
                is_array = False
                # 计算基础类型大小和对齐
                if base_type == 'char':
                    total_size = 1
                    alignment = 1
                elif base_type == 'int':
                    total_size = 4
                    alignment = 4
                elif base_type == 'double':
                    total_size = 8
                    alignment = 8
                elif base_type == 'short':
                    total_size = 2
                    alignment = 2
                elif base_type == 'float':
                    total_size = 4
                    alignment = 4
                else:
                    total_size = 1
                    alignment = 1
            
            self.type_definitions[type_name] = TypeInfo(
                type_name=type_name,
                base_type=base_type,
                array_size=array_size,
                is_array=is_array,
                total_size=total_size,
                alignment=alignment
            )
        
        print(f"# 解析完成，共找到 {len(self.type_definitions)} 个类型定义")
    
    def extract_chinese_name(self, comment: str) -> str:
        """提取中文注释 - 只从注释中提取，不推断"""
        if not comment:
            return ""
        
        # 去除前后空白
        comment = comment.strip()
        
        # 移除常见的类型前缀
        comment = re.sub(r'^TThostFtdc\w+Type\s*[-\s]*', '', comment)
        
        # 查找中文字符序列
        chinese_pattern = r'[\u4e00-\u9fff]+'
        matches = re.findall(chinese_pattern, comment)
        
        if matches:
            # 取第一个中文片段
            result = matches[0]
            # 如果有多个中文片段，连接起来
            if len(matches) > 1:
                full_text = ''.join(matches)
                if len(full_text) <= 20:  # 合理的字段名长度
                    result = full_text
            return result
        
        return ""
    
    def calculate_padding(self, current_offset: int, field_alignment: int) -> int:
        """计算需要的padding字节数"""
        if field_alignment <= 1:
            return 0
        
        aligned_offset = (current_offset + field_alignment - 1) // field_alignment * field_alignment
        return aligned_offset - current_offset
    
    def parse_struct_fields(self, header_file: str, struct_info: StructInfo) -> None:
        """解析结构体字段并计算padding - 使用上一行注释作为中文名"""
        if not os.path.exists(header_file):
            print(f"# 错误: 头文件不存在 {header_file}")
            return
        
        with open(header_file, 'r', encoding='gb2312', errors='ignore') as f:
            content = f.read()
        
        # 查找结构体定义
        struct_pattern = rf'struct\s+{re.escape(struct_info.cpp_name)}\s*\{{([^}}]+)\}}'
        match = re.search(struct_pattern, content, re.DOTALL)
        
        if not match:
            print(f"# 错误: 未找到结构体 {struct_info.cpp_name}")
            return
        
        struct_body = match.group(1)
        lines = struct_body.split('\n')
        print(lines)
        
        current_offset = 0
        fields = []
        padding_count = 0
        max_alignment = 1
        
        # 按行处理，记录前一行注释
        prev_line_comment = ""
        
        for line in lines:
            line = line.strip()
            print(line)
            
            # 跳过空行
            if not line:
                continue
            
            # 如果是注释行，记录为前一行注释
            if line.startswith('//') or line.startswith('/*') or line.startswith('*'):
                prev_line_comment = line.lstrip('/*').strip()
                continue
            
            # 查找字段定义（以分号结尾）
            if not line.endswith(';'):
                prev_line_comment = ""  # 重置注释
                continue
            
            # 分离字段定义和行内注释
            parts = line.split('//', 1)
            field_def = parts[0].strip().rstrip(';')
            inline_comment = parts[1].strip() if len(parts) > 1 else ""
            
            # 解析字段定义
            tokens = field_def.split()
            if len(tokens) < 2:
                prev_line_comment = ""  # 重置注释
                continue
            
            field_type = tokens[0]
            field_name = tokens[-1]
            
            # 去除字段名中的数组标记
            if '[' in field_name:
                field_name = field_name.split('[')[0]
            
            # 查找类型信息
            type_info = self.type_definitions.get(field_type)
            if type_info:
                field_size = type_info.total_size
                base_type = type_info.base_type
                is_array = type_info.is_array
                array_size = type_info.array_size
                field_alignment = type_info.alignment
            else:
                # 默认处理未知类型
                field_size = 1
                base_type = "char"
                is_array = False
                array_size = 0
                field_alignment = 1
                print(f"# 警告: 未找到类型定义 {field_type}")
            
            # 计算padding
            padding_size = self.calculate_padding(current_offset, field_alignment)
            if padding_size > 0:
                # 添加padding字段
                padding_field = FieldInfo(
                    field_name=f"_padding_{padding_count}",
                    type_name="char",
                    base_type="char",
                    comment=f"对齐填充{padding_size}字节",
                    cn_name="",
                    size=padding_size,
                    offset=current_offset,
                    alignment=1,
                    is_array=True,
                    array_size=padding_size,
                    is_padding=True
                )
                fields.append(padding_field)
                current_offset += padding_size
                padding_count += 1
                print(f"# Padding: _padding_{padding_count-1} {padding_size}字节 偏移:{current_offset-padding_size}")
            
            # 提取中文名称 - 优先使用前一行注释，其次使用行内注释
            cn_name = ""
            if prev_line_comment:
                cn_name = self.extract_chinese_name(prev_line_comment)
            if not cn_name and inline_comment:
                cn_name = self.extract_chinese_name(inline_comment)
            
            # 创建字段信息
            field = FieldInfo(
                field_name=field_name,
                type_name=field_type,
                base_type=base_type,
                comment=inline_comment or prev_line_comment,
                cn_name=cn_name,
                size=field_size,
                offset=current_offset,
                alignment=field_alignment,
                is_array=is_array,
                array_size=array_size,
                is_padding=False
            )
            
            fields.append(field)
            current_offset += field_size
            max_alignment = max(max_alignment, field_alignment)
            
            print(f"# 字段: {field_name} ({field_type}) {field_size}字节 偏移:{current_offset-field_size} 对齐:{field_alignment}")
            if cn_name:
                print(f"#   中文名: {cn_name}")
            if prev_line_comment:
                print(f"#   前行注释: {prev_line_comment}")
            
            # 重置前一行注释
            prev_line_comment = ""
        
        # 结构体末尾对齐
        final_padding = self.calculate_padding(current_offset, max_alignment)
        if final_padding > 0:
            padding_field = FieldInfo(
                field_name=f"_padding_final",
                type_name="char",
                base_type="char",
                comment=f"结构体末尾填充{final_padding}字节",
                cn_name="",
                size=final_padding,
                offset=current_offset,
                alignment=1,
                is_array=True,
                array_size=final_padding,
                is_padding=True
            )
            fields.append(padding_field)
            current_offset += final_padding
            print(f"# Final Padding: _padding_final {final_padding}字节 偏移:{current_offset-final_padding}")
        
        struct_info.fields = fields
        struct_info.total_size = current_offset - final_padding  # 不含padding的大小
        struct_info.aligned_size = current_offset  # 含padding的大小
        print(f"# 解析完成，共 {len([f for f in fields if not f.is_padding])} 个字段，原始大小 {struct_info.total_size} 字节，对齐后大小 {struct_info.aligned_size} 字节")
        print(f"# 收集到 {len([f for f in fields if f.cn_name and not f.is_padding])} 个字段的中文名")
    
    def get_python_type(self, field: FieldInfo) -> str:
        """获取Python ctypes类型"""
        if field.is_padding:
            return f"ctypes.c_char * {field.array_size}"
        elif field.is_array:
            return f"ctypes.c_char * {field.array_size}"
        elif field.base_type == "char":
            return "ctypes.c_char"
        elif field.base_type == "int":
            return "ctypes.c_int"
        elif field.base_type == "double":
            return "ctypes.c_double"
        elif field.base_type == "short":
            return "ctypes.c_short"
        elif field.base_type == "float":
            return "ctypes.c_float"
        else:
            return "ctypes.c_char"
    
    def generate_python_struct(self, struct_info: StructInfo) -> str:
        """生成Python结构体定义"""
        lines = []
        lines.append(f"class {struct_info.python_name}(ctypes.Structure):")
        lines.append(f'    """{struct_info.description} - 对应{struct_info.cpp_name}"""')
        lines.append("    _pack_ = 1")
        lines.append("    _fields_ = [")
        
        for field in struct_info.fields:
            python_type = self.get_python_type(field)
            
            if field.is_padding:
                lines.append(f'        ("{field.field_name}", {python_type}),  # {field.comment}')
            else:
                comment_parts = [field.type_name]
                if field.cn_name:
                    comment_parts.append(field.cn_name)
                comment = " - ".join(comment_parts)
                lines.append(f'        ("{field.field_name}", {python_type}),  # {comment}')
        
        lines.append("    ]")
        lines.append(f"    # 原始大小: {struct_info.total_size} 字节，对齐后大小: {struct_info.aligned_size} 字节")
        lines.append("")
        
        return "\n".join(lines)
    
    def generate_field_mapping(self, struct_info: StructInfo) -> str:
        """生成字段中文名映射"""
        lines = []
        lines.append(f"# {struct_info.python_name} 字段中文名映射")
        lines.append(f"{struct_info.python_name.upper()}_FIELD_NAMES_CN = {{")
        
        for field in struct_info.fields:
            if field.cn_name and not field.is_padding:
                lines.append(f'    "{field.field_name}": "{field.cn_name}",')
        
        lines.append("}")
        lines.append("")
        
        return "\n".join(lines)
        
    def generate_all_structs(self, input_file: str, output_file: str) -> None:
        """生成所有结构体定义"""
        # 解析类型定义
        self.parse_typedefs("lib/headers/ThostFtdcUserApiDataType.h")
        
        # 生成文件头
        output_lines = [
            "#!/usr/bin/env python3",
            "# -*- coding: utf-8 -*-",
            '"""',
            "CTP结构体定义带padding完整版本",
            "包含字段名、类型、大小、对齐和中文名称",
            '"""',
            "import ctypes",
            ""
        ]
        
        # 解析并生成每个结构体
        for cpp_name, python_name, description in self.target_structs:
            print(f"\n=== 解析 {python_name} ===")
            
            struct_info = StructInfo(
                cpp_name=cpp_name,
                python_name=python_name,
                description=description,
                fields=[]
            )
            
            self.parse_struct_fields(input_file, struct_info)
            
            output_lines.append(f"# === {python_name} ===")
            output_lines.append(self.generate_python_struct(struct_info))
            output_lines.append(self.generate_field_mapping(struct_info))
        

        # 写入文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(output_lines))
        
        print(f"\n结构体定义已生成到: {output_file}")

if __name__ == "__main__":
    parser = CTPStructParser()
    parser.generate_all_structs("lib/headers/ThostFtdcUserApiStruct.h", "trader/ctp_structures.py")

