#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的直接数据传递功能
包括查询、下单和回调的直接接口
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)


def test_direct():    
    print("开始测试完整的直接数据传递功能...")
    print("="*80)
    
    try:    
        from trader.ctp_direct_wrapper import CTPDirectWrapper
        service = CTPDirectWrapper()
        service.initialize()
        for _ in range(10):
            if service.is_logged_in():
                break
            time.sleep(1)  
        
        print(service.connection_config)

        initial_account = service.query_trading_account()
        
        
        # 合约查询
        instrument_params = {'instrument_id': "IH2512"} 
        instrument_result = service.query_instrument(**instrument_params)
        
        # 测试直接下单功能
        print("\n" + "="*80)
        print("测试直接下单功能")

        # 打印下单输入参数
        order_params = {
            'instrument_id': "IH2512",
            'direction': "买",
            'offset_flag': "开仓",
            'price': 2536,
            'volume': 1,
            'exchange_id': "CFFEX"
        }
        order_result = service.order_insert(**order_params)
        
        # 等待一段时间让订单处理
        time.sleep(2)
        
        after_order_query = service.query_order()
                
        # 最终成交查询
        final_trade = service.query_trade()
        
        # 最终持仓查询
        final_position = service.query_investor_position()
        
    except Exception as e:
        import traceback
        traceback.print_exc()
    
if __name__ == "__main__":
    test_direct()
